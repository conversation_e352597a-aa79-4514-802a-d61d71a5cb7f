<header class="navbar navbar-expand-lg fixed-top bg-light">
  <div class="container">
    <component-logo-box [className]="'pe-sm-3'" />

    <app-theme-switcher
      class="form-check form-switch mode-switch order-lg-2 me-3 me-lg-4 ms-auto"
    />

    <div class="nav align-items-center order-lg-3 ms-n1 me-3 me-sm-0">
      <a
        class="nav-link fs-4 p-2 mx-sm-1"
        (click)="openWindowCustomClass(content)"
        href="javascript:void(0); "
        data-bs-toggle="modal"
        aria-label="Search"
      >
        <i class="ai-search"></i>
      </a>
      <a
        class="nav-link fs-4 p-2 mx-sm-1 d-none d-sm-flex"
        routerLink="/auth/sign-in"
        aria-label="Account"
      >
        <i class="ai-user"></i>
      </a>
      <a
        (click)="offCanvasOpen(contents)"
        class="nav-link position-relative fs-4 p-2"
        href="javascript:void(0);"
        data-bs-toggle="offcanvas"
        aria-label="Shopping cart"
      >
        <i class="ai-cart"></i>
        <span
          class="badge bg-primary fs-xs position-absolute end-0 top-0 me-n1"
          style="padding: 0.25rem 0.375rem"
          >3</span
        >
      </a>
    </div>

    <vertical-menu-button></vertical-menu-button>

    <nav
      class="collapse navbar-collapse"
      #collapse="ngbCollapse"
      [(ngbCollapse)]="isMenuCollapsed"
      id="navbarNav"
    >
      <horizontal-app-menu />
    </nav>
  </div>
</header>

<!-- Search modal -->
<ng-template
  #content
  let-modal
  id="searchModal"
  tabindex="-1"
  role="dialog"
  data-focus-input="#search"
>
  <div class="modal-header d-block position-relative border-0 pb-3">
    <form class="position-relative w-100 mt-2 mb-4">
      <button
        class="btn-close position-absolute top-50 end-0 translate-middle-y m-0 me-n1"
        (click)="modal.close('Close click')"
        type="reset"
        data-bs-dismiss="modal"
        aria-label="Close"
      ></button>
      <i
        class="ai-search fs-xl position-absolute top-50 start-0 translate-middle-y ms-3"
      ></i>
      <input
        class="form-control form-control-lg px-5"
        type="search"
        placeholder="Type to search"
        data-focus-on-open='["modal", "#searchModal"]'
      />
    </form>
    <div class="fs-xs fw-medium text-body-secondary text-uppercase">
      Suggestions
    </div>
  </div>
  <div class="modal-body pt-3">
    <div class="d-flex align-items-center border-bottom pb-4 mb-4">
      <a
        class="position-relative d-inline-block flex-shrink-0 bg-secondary rounded-1"
        routerLink="/shop/product"
      >
        <span
          class="badge bg-success position-absolute top-0 start-100 translate-middle ms-n1"
          >Shop</span
        >
        <img src="assets/img/shop/cart/01.png" width="90" alt="Product" />
      </a>
      <div class="ps-3">
        <h4 class="h6 mb-2">
          <a routerLink="/shop/product">Candle in concrete bowl</a>
        </h4>
        <span class="mb-0 me-2">$11.00</span>
        <del class="fs-sm text-body-secondary ms-auto">$15.00</del>
      </div>
    </div>
    <div class="d-flex align-items-center border-bottom pb-4 mb-4">
      <a class="position-relative d-inline-block flex-shrink-0" href="#">
        <span
          class="badge bg-info position-absolute top-0 start-100 translate-middle ms-n1"
          >Blog</span
        >
        <img
          class="rounded-1"
          src="assets/img/landing/shop-1/instagram/02.jpg"
          width="90"
          alt="Post"
        />
      </a>
      <div class="ps-3">
        <h4 class="h6 mb-0">
          <a href="#"
            >Bedroom decoration explained. Tips &amp; tricks from the field
            experts.</a
          >
        </h4>
      </div>
    </div>
    <div class="d-flex align-items-center">
      <a
        class="position-relative d-inline-block flex-shrink-0 bg-secondary rounded-1"
        routerLink="/shop/product"
      >
        <span
          class="badge bg-success position-absolute top-0 start-100 translate-middle ms-n1"
          >Shop</span
        >
        <img src="assets/img/shop/cart/02.png" width="90" alt="Product" />
      </a>
      <div class="ps-3">
        <h4 class="h6 mb-2">
          <a routerLink="/shop/product">Exquisite glass vase</a>
        </h4>
        <span class="mb-0 me-2">$23.00</span>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #contents let-offcanvas>
  <div class="px-4 pt-3 py-2">
    <div
      class="d-flex justify-content-between align-items-center border-bottom pb-3 pb-sm-4"
    >
      <h2 class="offcanvas-title d-flex align-items-center mb-1">
        Your cart
        <span class="fs-base fw-normal text-body-secondary ms-3"
          >(3 items)</span
        >
      </h2>
      <button
        class="btn-close mb-1 me-n1"
        type="button"
        (click)="offcanvas.close('Close Click')"
        data-bs-dismiss="offcanvas"
        data-bs-target="#cartOffcanvas"
        aria-label="Close"
      ></button>
    </div>
  </div>

  <div class="offcanvas-body">
    @for (item of cartItems; track $index; let first = $first) {
      <div class="d-sm-flex align-items-center py-4">
        <a
          class="d-inline-block flex-shrink-0 bg-secondary rounded-1 p-sm-2 p-xl-3 mb-2 mb-sm-0"
          routerLink="/contacts/v1"
        >
          <img [src]="item.image" width="110" alt="Product" />
        </a>
        <div class="w-100 pt-1 ps-sm-4">
          <div class="d-flex">
            <div class="me-3">
              <h3 class="h5 mb-2">
                <a routerLink="/contacts/v1">{{ item.name }}</a>
              </h3>
              @if (first) {
                <div class="d-sm-flex flex-wrap">
                  <div class="text-body-secondary fs-sm me-3">
                    Color: <span class="text-dark fw-medium">Gray night</span>
                  </div>
                  <div class="text-body-secondary fs-sm me-3">
                    Weight: <span class="text-dark fw-medium">140g</span>
                  </div>
                </div>
              }
            </div>
            <div class="text-end ms-auto">
              <div class="fs-5 mb-2">
                {{ selectedCurrency }}{{ item.price }}.00
              </div>
              @if (item.discount) {
                <del class="text-body-secondary ms-auto"
                  >{{ selectedCurrency }}{{ item.discount }}.00</del
                >
              }
            </div>
          </div>
          <div class="count-input ms-n3">
            <button
              class="btn btn-icon fs-xl"
              type="button"
              (click)="decrementCounter($index)"
            >
              -
            </button>
            <input
              class="form-control"
              type="number"
              [value]="item.quantity"
              readonly
            />
            <button
              class="btn btn-icon fs-xl"
              type="button"
              (click)="incrementCounter($index)"
            >
              +
            </button>
          </div>
          <div class="nav justify-content-end mt-n5 mt-sm-n3">
            <a
              class="nav-link fs-xl p-2"
              href="javascript:void(0);"
              ngbTooltip="Remove"
              aria-label="Remove"
            >
              <i class="ai-trash"></i>
            </a>
          </div>
        </div>
      </div>
    }
  </div>
  <div class="px-4 pb-3 pb-sm-4 pb-sm-5">
    <div class="d-sm-flex align-items-center border-top pt-4">
      <div
        class="input-group input-group-sm border-dashed mb-3 mb-sm-0 me-sm-4 me-md-5"
      >
        <input
          class="form-control text-uppercase"
          type="text"
          placeholder="Your coupon code"
        />
        <button class="btn btn-secondary" type="button">Apply coupon</button>
      </div>
      <div class="d-flex align-items-center justify-content-center">
        <span class="fs-xl me-3">Total:</span>
        <span class="h3 mb-0">{{ selectedCurrency }}92.00</span>
      </div>
    </div>
  </div>

  <div class="d-flex align-items-center justify-content-between px-4 pb-3">
    <div class="nav d-none d-sm-block">
      <a
        class="nav-link fw-semibold px-0"
        href="#cartOffcanvas"
        data-bs-dismiss="offcanvas"
      >
        <i class="ai-chevron-left fs-xl me-2"></i>
        Back to shop
      </a>
    </div>
    <a
      class="btn btn-lg btn-primary w-100 w-sm-auto"
      routerLink="/shop/checkout"
    >
      Proceed to checkout
      <i class="ai-chevron-right ms-2 me-n1"></i>
    </a>
  </div>
</ng-template>
