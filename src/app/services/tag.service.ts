import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { TagDataResponse } from '../models/tag.model';

@Injectable({
  providedIn: 'root'
})
export class TagService {

  constructor(private httpClient: HttpClient) { }

  
  getAll():Observable<TagDataResponse>{
    const apiUrl = 'http://localhost:5071/api/v1/Tag';
    return this.httpClient.get<TagDataResponse>(apiUrl).pipe();
  }
}
