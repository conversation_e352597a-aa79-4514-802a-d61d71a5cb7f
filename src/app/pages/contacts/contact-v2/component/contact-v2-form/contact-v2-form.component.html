<div class="row pb-1 pb-sm-3 pb-lg-4">
  <div class="col-lg-4 pe-xxl-4">
    <h1 class="display-2">Contacts</h1>
    <p class="fs-lg pb-4 mb-0 mb-sm-2">
      Get in touch with us by completing the below form or call us now
    </p>
  </div>
  <div class="col-lg-8 col-xl-7 offset-xl-1">
    <form
      class="row g-4 needs-validation"
      novalidate
      [formGroup]="contactForm"
      (ngSubmit)="submitForm()"
    >
      <div class="col-sm-6">
        <label class="form-label fs-base" for="name">Name</label>
        <input
          class="form-control form-control-lg"
          formControlName="name"
          type="text"
          placeholder="Your name"
          required
          id="name"
        />
        @if (formGroup && formControl['name'].errors) {
          <div class="invalid-feedback d-block">Please enter your name!</div>
        }
      </div>
      <div class="col-sm-6">
        <label class="form-label fs-base" for="email">Email</label>
        <input
          class="form-control form-control-lg"
          type="email"
          formControlName="email"
          placeholder="Email address"
          required
          id="email"
        />
        @if (formGroup && formControl['email'].errors) {
          <div class="invalid-feedback d-block">
            Please provide a valid email address!
          </div>
        }
      </div>
      <div class="col-sm-6">
        <label class="form-label fs-base" for="phone">Phone</label>
        <input
          class="form-control form-control-lg"
          type="text"
          formControlName="phone"
          placeholder="Phone number"
          id="phone"
        />
        @if (formGroup && formControl['phone'].errors) {
          <div class="invalid-feedback d-block">Please enter your number</div>
        }
      </div>
      <div class="col-sm-6">
        <label class="form-label fs-base" for="location">Location</label>
        <select
          class="form-select form-select-lg"
          id="location"
          formControlName="location"
        >
          <option value="All locations" [defaultSelected]="'All locations'">
            All locations
          </option>
          <option value="Asia and Pacific">Asia and Pacific</option>
          <option value="Central Europe">Central Europe</option>
          <option value="Eastern Europe">Eastern Europe</option>
          <option value="North America">North America</option>
          <option value="South America">South America</option>
        </select>
      </div>
      <div class="col-sm-12">
        <label class="form-label fs-base" for="message">How can we help?</label>
        <textarea
          class="form-control form-control-lg"
          rows="5"
          formControlName="message"
          placeholder="Enter your message here..."
          required
          id="message"
        ></textarea>
        @if (formGroup && formControl['message'].errors) {
          <div class="invalid-feedback d-block">Please enter your message!</div>
        }
      </div>
      <div class="col-sm-12">
        <div class="form-check form-check-inline">
          <input class="form-check-input" type="checkbox" id="agree" />
          <label class="form-check-label" for="agree"
            >I agree to the
            <a
              class="nav-link d-inline fs-normal text-decoration-underline p-0"
              href="javascript:void(0);"
              >Terms &amp; Conditions</a
            ></label
          >
        </div>
      </div>
      <div class="col-sm-12 pt-2">
        <button type="submit" class="btn btn-lg btn-primary" type="submit">
          Send a request
        </button>
      </div>
    </form>
  </div>
</div>
