<section class="position-relative overflow-hidden pt-5">
  <div
    class="container position-relative z-2 pt-4 pt-sm-5 mt-2 mt-sm-0 mt-md-2 mt-lg-3 mt-xl-4 mt-xxl-5"
  >
    <div class="row justify-content-center py-4">
      <div class="col-lg-11 col-xl-10 text-center pb-2 pb-sm-3 mb-lg-2 mb-xl-3">
        <h1 class="display-2 fw-normal mb-0">
          <span class="fw-bold">Segment your audience</span> using a variety of
          methods
        </h1>
      </div>
    </div>
    <div class="parallax" appParallax [parallaxScale]="5">
      @for (item of parallaxImageData; track $index) {
        <div class="parallax-layer" [attr.data-depth]="item.depth">
          <img class="d-dark-mode-none" [src]="item.light_image" alt="Layer" />
          <img
            class="d-none d-dark-mode-block"
            [src]="item.dark_image"
            alt="Layer"
          />
        </div>
      }
    </div>
  </div>
</section>
