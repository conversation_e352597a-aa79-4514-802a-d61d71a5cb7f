<section class="container pt-5 mt-md-3 mt-lg-2 mt-xl-4 mt-xxl-5">
  <h2 class="h1 text-center pt-3 pt-sm-4 pt-lg-5 pb-3 mb-3 mb-lg-4 mt-xl-3">
    Our benefits
  </h2>
  <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-4">
    @for (item of benefitCardData; track $index) {
      <div class="col">
        <div class="card card-flip">
          <div class="card-flip-inner">
            <div
              class="card-flip-front bg-secondary rounded-5 py-2 py-lg-4 px-0"
            >
              <div class="card-body text-center">
                <div
                  [innerHTML]="item.item.card_flip_front.sanitizedIconFront"
                ></div>
                <h3>{{ item.item.card_flip_front.heading }}</h3>
                <p class="card-text">
                  {{ item.item.card_flip_front.paragraph }}
                </p>
              </div>
            </div>
            <div
              class="card-flip-back {{
                item.item.card_flip_back.color
              }} rounded-5 py-2 py-lg-4 px-0"
            >
              <div class="card-body text-center">
                <div
                  [innerHTML]="item.item.card_flip_back.sanitizedIconBack"
                ></div>
                <div class="h3 display-4 text-white pb-1 mb-2">
                  {{ item.item.card_flip_back.heading }}
                </div>
                <p class="card-text text-white opacity-70">
                  {{ item.item.card_flip_back.paragraph }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    }
  </div>
</section>
