<section class="position-relative pt-5 mt-2 mt-xl-4 mt-xxl-5">
  <div
    class="bg-secondary position-absolute start-0 bottom-0 w-100"
    style="height: 252px"
  ></div>
  <div class="container py-3 pt-sm-4 pt-lg-5 mt-xl-2 mt-xxl-3 mb-3 mb-lg-4">
    <div class="fs-sm text-uppercase mb-3">Where</div>
    <div class="d-sm-flex align-items-center justify-content-between">
      <h2 class="display-6 mb-0 me-sm-4">We are located in London</h2>

      <div class="d-none d-sm-flex">
        <button
          class="prev-gallery btn btn-icon btn-outline-primary rounded-circle me-3"
          type="button"
          aria-label="Prev"
        >
          <i class="ai-arrow-left"></i>
        </button>
        <button
          class="next-gallery btn btn-icon btn-outline-primary rounded-circle"
          type="button"
          aria-label="Next"
        >
          <i class="ai-arrow-right"></i>
        </button>
      </div>
    </div>
  </div>

  <div class="container-start position-relative z-2 pe-0">
    <div class="swiper">
      <swiper-container
        class="swiper-wrapper gallery align-items-end"
        [config]="swiperConfig"
        init="false"
      >
        @for (item of locationData; track $index) {
          <swiper-slide class="swiper-slide w-auto gallery">
            <a
              class="d-block card-hover zoom-effect"
              [style.max-width]="item.thumb"
              (click)="open($index)"
            >
              <div
                class="d-flex justify-content-center align-items-center position-absolute top-0 start-0 w-100 h-100 rounded-5 overflow-hidden z-2 opacity-0"
              >
                <i class="ai-zoom-in fs-2 text-white position-relative z-2"></i>
                <div
                  class="position-absolute top-0 start-0 w-100 h-100 bg-black opacity-40"
                ></div>
              </div>
              <div class="zoom-effect-wrapper rounded-5">
                <div class="zoom-effect-img">
                  <img src="{{ item.src }}" alt="Gallery image #1" />
                </div>
              </div>
            </a>
          </swiper-slide>
        }
      </swiper-container>
    </div>

    <div class="d-flex d-sm-none pt-4">
      <button
        class="prev-gallery btn btn-icon btn-outline-primary rounded-circle me-3"
        type="button"
        aria-label="Prev"
      >
        <i class="ai-arrow-left"></i>
      </button>
      <button
        class="next-gallery btn btn-icon btn-outline-primary rounded-circle"
        type="button"
        aria-label="Next"
      >
        <i class="ai-arrow-right"></i>
      </button>
    </div>
  </div>
</section>
