<section class="container pb-2 pb-sm-3 pb-md-4 pb-lg-5 mb-xl-3 mb-xxl-5">
  <div class="bg-light rounded-5 py-4 py-md-5 px-lg-5">
    <div class="row row-cols-2 row-cols-md-4 g-0">
      @for (item of counterData; track $index) {
        <div
          class="col d-md-flex justify-content-center text-center text-md-start position-relative"
        >
          <div
            class="position-absolute top-50 end-0 translate-middle-y border-end"
            style="height: 60px"
          ></div>
          <div class="p-3 px-sm-0 py-sm-4">
            <div class="h2 display-5 text-primary mb-0">{{ item.count }}</div>
            <span>{{ item.title }}</span>
          </div>
        </div>
      }
    </div>
  </div>
</section>
