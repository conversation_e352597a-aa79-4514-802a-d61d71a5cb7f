<section class="container" id="tickets">
  <div class="row border border-light rounded-5 overflow-hidden g-0">
    <div class="col-md-7 col-xl-8">
      <div
        class="card h-100 bg-dark border-0 rounded-0 py-md-3 py-lg-4 py-xl-5 px-3 px-sm-4 px-lg-5"
        data-bs-theme="dark"
      >
        <div
          class="position-absolute top-0 start-0 w-100 h-100"
          style="
            background-image: url(assets/img/landing/conference/noise.png);
            mix-blend-mode: soft-light;
            opacity: 12%;
          "
        ></div>
        <div
          class="card-body position-relative z-2 py-5 px-2 mx-auto"
          style="max-width: 732px"
        >
          <h2 class="h1">In-Person Experience</h2>
          <p class="pb-3 mb-lg-4">
            Aenean sollicitudin sit convallis auctor egestas urna massa semper
            ultrices libero suspendisse venenatis urna ac in vitae pharetra
            aliquam.
          </p>
          <div class="display-3 text-primary fw-bold pb-3 mb-1 mb-lg-3">
            {{ selectedCurrency }}299
          </div>
          <ul class="list-unstyled mb-0">
            @for (item of content; track $index) {
              <li class="d-flex pt-1 mt-2">
                <i class="ai-circle-check text-primary fs-xl mt-1 me-2"></i>
                {{ item }}
              </li>
            }
          </ul>
        </div>
        <div class="card-footer w-100 border-0 pt-0 pb-5 px-2 mx-0">
          <button class="btn btn-lg btn-primary" type="button">
            Buy ticket
          </button>
        </div>
      </div>
    </div>
    <div
      class="col-md-5 col-xl-4 border-top border-md-0 border-start-md border-light"
    >
      <div
        class="card h-100 bg-secondary border-0 rounded-0 py-md-3 py-lg-4 py-xl-5 px-3 px-sm-4 px-lg-5"
      >
        <div class="card-body py-5 px-2">
          <h2 class="h1">Live Stream</h2>
          <p class="pb-3 mb-lg-4">
            Sit facilisis viverra ullamcorper vestibulum nunc tincidunt egestas.
          </p>
          <div class="display-3 text-primary fw-bold pb-3 mb-1 mb-lg-3">
            {{ selectedCurrency }}89
          </div>
          <ul class="list-unstyled mb-0">
            @for (item of stream; track $index) {
              <li class="d-flex pt-1 mt-2">
                <i class="ai-circle-check text-primary fs-xl mt-1 me-2"></i>
                {{ item }}
              </li>
            }
          </ul>
        </div>
        <div class="card-footer w-100 border-0 pt-0 pb-5 px-2 mx-0">
          <button class="btn btn-lg btn-primary" type="button">
            Buy ticket
          </button>
        </div>
      </div>
    </div>
  </div>
</section>
