<section class="bg-primary position-relative py-5" data-bs-theme="light">
  <div
    class="bg-dark position-absolute top-0 start-0 w-100 h-100 opacity-5"
  ></div>
  <div
    class="position-absolute top-0 start-0 w-100 h-100 opacity-15"
    style="
      background-image: url(assets/img/landing/conference/noise.png);
      mix-blend-mode: soft-light;
    "
  ></div>
  <div class="container position-relative z-2 pt-sm-2 pb-3 py-md-4 py-lg-5">
    <div class="row align-items-center">
      <div class="col-md-6 col-lg-5 col-xxl-4 pb-2 pb-md-0 mb-4 mb-md-0">
        <div class="text-warning pb-3 mb-1 mb-md-2 mb-lg-3">
          <svg
            class="d-block"
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 0L14.659 4.14233C15.4424 9.06596 20.2555 12.9977 26.4868 13.8042L28 14L26.4868 14.1958C20.2555 15.0023 15.4424 18.934 14.659 23.8577L14 28L13.341 23.8577C12.5576 18.934 7.74447 15.0023 1.51323 14.1958L0 14L1.51323 13.8042C7.74447 12.9977 12.5576 9.06596 13.341 4.14234L14 0Z"
            ></path>
          </svg>
        </div>
        <h2 class="display-5 text-light mb-4 mb-md-5">
          This is the event that can not be missed!
        </h2>
        <div class="d-flex align-items-center">
          <div class="d-flex me-3">
            <div
              class="bg-white flex-shrink-0 rounded-circle position-relative z-4"
              style="padding: 3px"
            >
              <img
                class="rounded-circle"
                src="assets/img/avatar/05.jpg"
                width="48"
                alt="Avatar"
              />
            </div>
            <div
              class="bg-white flex-shrink-0 rounded-circle position-relative z-3 ms-n3"
              style="padding: 3px"
            >
              <img
                class="rounded-circle"
                src="assets/img/avatar/08.jpg"
                width="48"
                alt="Avatar"
              />
            </div>
            <div
              class="bg-white flex-shrink-0 rounded-circle position-relative z-2 ms-n3"
              style="padding: 3px"
            >
              <img
                class="rounded-circle"
                src="assets/img/avatar/09.jpg"
                width="48"
                alt="Avatar"
              />
            </div>
            <div
              class="bg-white flex-shrink-0 rounded-circle position-relative z-1 ms-n3"
              style="padding: 3px"
            >
              <img
                class="rounded-circle"
                src="assets/img/avatar/12.jpg"
                width="48"
                alt="Avatar"
              />
            </div>
          </div>
          <p class="text-light mb-0">
            <strong>1.5k</strong>
            <span class="text-light opacity-70">&nbsp;attendees</span>
          </p>
        </div>
      </div>
      <div class="col-md-6 offset-lg-1 offset-xxl-2">
        <div class="card bg-white border-0 p-1 p-sm-2 p-lg-3">
          <form
            [formGroup]="contactForm"
            (ngSubmit)="submitForm()"
            class="card-body needs-validation p-4"
            novalidate
          >
            <div class="mb-4">
              <label class="form-label fs-base" for="email">Email</label>
              <input
                class="form-control form-control-lg"
                type="email"
                placeholder="Email address"
                formControlName="email"
                [ngClass]="{
                  'is-invalid': submit && form['email'].errors,
                }"
                required
                id="email"
              />
              <div class="invalid-feedback">
                Please provide a valid email address!
              </div>
            </div>
            <div class="mb-4">
              <label class="form-label fs-base" for="name">Name</label>
              <input
                class="form-control form-control-lg"
                type="text"
                placeholder="Your name"
                formControlName="name"
                [ngClass]="{ 'is-invalid': submit && form['name'].errors }"
                required
                id="name"
              />
              <div class="invalid-feedback">Please enter your name!</div>
            </div>
            <div class="mb-4">
              <label class="form-label fs-base" for="message">Message</label>
              <textarea
                class="form-control form-control-lg"
                rows="5"
                placeholder="Your message"
                formControlName="message"
                [ngClass]="{ 'is-invalid': submit && form['message'].errors }"
                required
                id="message"
              ></textarea>
              <div class="invalid-feedback">Please enter your message!</div>
            </div>
            <button class="btn btn-lg btn-warning" type="submit">
              Send request
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>
