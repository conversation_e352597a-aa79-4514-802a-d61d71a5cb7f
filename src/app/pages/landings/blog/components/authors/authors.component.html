<section class="container-start pt-xl-2 pb-5 mb-2 mb-lg-3 mb-xl-4 mb-xxl-5">
  <div class="row g-0 pb-md-4 pb-lg-5">
    <div class="col-xl-2">
      <div
        class="d-flex flex-xl-column align-items-center align-items-xl-start pe-xl-5 pb-3 pb-lg-4 mb-3"
      >
        <h2 class="h1 mb-0 mb-xl-5">Top authors</h2>

        <div class="d-flex ms-auto ms-xl-0 pe-sm-3 pe-xl-0">
          <button
            class="btn btn-sm btn-icon btn-outline-primary rounded-circle me-3"
            type="button"
            id="prev-author"
            aria-label="Prev"
          >
            <i class="ai-arrow-left"></i>
          </button>
          <button
            class="btn btn-sm btn-icon btn-outline-primary rounded-circle"
            type="button"
            id="next-author"
            aria-label="Next"
          >
            <i class="ai-arrow-right"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="col-xl-9 offset-xl-1">
      <div class="swiper">
        <swiper-container
          [config]="swiperConfig"
          init="false"
          class="swiper-wrapper"
        >
          @for (item of topAuthorBlogData; track $index) {
            <swiper-slide class="swiper-slide w-xxl-auto">
              <a
                class="d-block card-hover text-decoration-none mx-auto"
                href="javascript:void(0);"
                style="max-width: 416px"
              >
                <div
                  class="bg-secondary rounded-5 position-relative overflow-hidden"
                >
                  <div
                    class="position-absolute top-0 start-0 w-100 h-100 bg-size-cover bg-position-center opacity-0"
                    style="
                      background-image: url(assets/img/landing/blog/authors/hover.svg);
                    "
                  ></div>
                  <img
                    class="position-relative z-2"
                    [src]="item.image"
                    alt="Author picture"
                  />
                </div>
                <div class="border-bottom pt-4 pb-3">
                  <h3 class="h4 mb-1">{{ item.name }}</h3>
                  <p class="text-body-secondary mb-0">{{ item.role }}</p>
                </div>
              </a>
            </swiper-slide>
          }
        </swiper-container>
      </div>
    </div>
  </div>
</section>
