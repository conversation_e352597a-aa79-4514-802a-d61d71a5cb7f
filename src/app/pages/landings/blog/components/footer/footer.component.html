<footer class="footer pt-lg-5 pt-4 pb-5">
  <div class="container">
    <div class="row gy-md-5 gy-4 mb-md-5 mb-4 pb-lg-2">
      <div class="col-lg-3">
        <component-logo-box
          className="d-inline-flex align-items-center mb-lg-4 mb-3"
        />

        <p
          class="mb-4 pb-lg-3 fs-xs text-body-secondary"
          style="max-width: 306px"
        >
          Morbi et massa fames ac scelerisque sit commodo dignissim faucibus vel
          quisque proin lectus laoreet sem adipiscing sollicitudin erat massa
          tellus lorem enim aenean phasellus in hendrerit
        </p>
        <div class="d-flex mt-n3 ms-n3">
          <a
            class="btn btn-secondary btn-icon btn-sm btn-instagram rounded-circle mt-3 ms-3"
            href="javascript:void(0);"
            aria-label="Instagram"
          >
            <i class="ai-instagram"></i>
          </a>
          <a
            class="btn btn-secondary btn-icon btn-sm btn-facebook rounded-circle mt-3 ms-3"
            href="javascript:void(0);"
            aria-label="Facebook"
          >
            <i class="ai-facebook"></i>
          </a>
          <a
            class="btn btn-secondary btn-icon btn-sm btn-linkedin rounded-circle mt-3 ms-3"
            href="javascript:void(0);"
            aria-label="LnkedIn"
          >
            <i class="ai-linkedin"></i>
          </a>
        </div>
      </div>
      <div class="col-xl-8 offset-xl-1 col-lg-9">
        <div class="row row-cols-sm-4 row-cols-1">
          <div class="col">
            <ul class="nav flex-column mb-0">
              @for (feature of features; track $index) {
                <li class="nav-item mb-2">
                  <a class="nav-link p-0" href="javascript:void(0);">{{
                    feature
                  }}</a>
                </li>
              }
            </ul>
          </div>
          <div class="col">
            <ul class="nav flex-column mb-0">
              @for (item of category; track $index) {
                <li class="nav-item mb-2">
                  <a class="nav-link p-0" href="javascript:void(0);">{{
                    item
                  }}</a>
                </li>
              }
            </ul>
          </div>
          <div class="col">
            <ul class="nav flex-column mb-0">
              @for (benefit of benefits; track $index) {
                <li class="nav-item mb-2">
                  <a class="nav-link p-0" href="javascript:void(0);">{{
                    benefit
                  }}</a>
                </li>
              }
            </ul>
          </div>
          <div class="col">
            <ul class="nav flex-column mb-0">
              <li class="nav-item mb-2">
                <a class="nav-link p-0 text-primary" href="javascript:void(0);"
                  >example&#64;gmail.com</a
                >
              </li>
              <li class="nav-item mb-2">
                <a class="nav-link p-0" href="javascript:void(0);"
                  >Privacy policy</a
                >
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <p class="nav fs-sm mb-0">
      <span class="text-body-secondary"
        >&copy; All rights reserved. Made by</span
      >
      <a
        class="nav-link d-inline fw-normal p-0 ms-1"
        href="{{ developBy }}"
        target="_blank"
        rel="noopener"
        >{{ author }}</a
      >
    </p>
    <div class="pt-4 pt-lg-0"></div>
  </div>
</footer>
