<section class="container py-5 my-md-2 my-lg-3 my-xl-4 my-xxl-5">
  <h2 class="h1 pb-3 py-md-4">Latest posts</h2>
  <div class="row pb-md-4 pb-lg-5">
    <div class="col-lg-6 pb-2 pb-lg-0 mb-4 mb-lg-0">
      <article
        class="card h-100 border-0 position-relative overflow-hidden bg-size-cover bg-position-center me-lg-4"
        style="background-image: url(assets/img/landing/blog/04.jpg)"
      >
        <div
          class="bg-dark position-absolute top-0 start-0 w-100 h-100 opacity-60"
        ></div>
        <div class="card-body d-flex flex-column position-relative z-2 mt-sm-5">
          <h3 class="pt-5 mt-4 mt-sm-5 mt-lg-auto">
            <a class="stretched-link text-light" routerLink="/shop/product"
              >Travel destinations to inspire and restore resources</a
            >
          </h3>
          <p class="card-text text-light opacity-70">
            Morbi et massa scelerisque sit commodo dignissim faucibus vel
            quisque proin lectus laoreet pharetra at condimentum...
          </p>
          <div class="d-flex align-items-center">
            <span class="fs-sm text-light opacity-50">9 hours ago</span>
            <span class="fs-xs text-light opacity-30 mx-3">|</span>
            <a
              class="badge text-ligh fs-xs border border-light"
              href="javascript:void(0);"
              >Travel</a
            >
          </div>
        </div>
      </article>
    </div>

    <div class="col-lg-6">
      <div class="row row-cols-1 row-cols-sm-2 g-4">
        @for (item of latestBlog; track $index) {
          <article class="col py-1 py-xl-2">
            <div class="border-bottom pb-4 ms-xl-3">
              <h3 class="h4">
                <a routerLink="{{ item.href }}">{{ item.title }}</a>
              </h3>
              <p>
                {{ item.excerpt }}
              </p>
              <div class="d-flex align-items-center">
                <span class="fs-sm text-body-secondary">{{ item.date }}</span>
                <span class="fs-xs opacity-20 mx-3">|</span>
                <a
                  class="badge text-nav fs-xs border"
                  href="javascript:void(0);"
                  >{{ item.category }}</a
                >
              </div>
            </div>
          </article>
        }
      </div>
    </div>
  </div>
</section>
