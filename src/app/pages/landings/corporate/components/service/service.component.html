<section class="container pt-5 mt-lg-3 mt-xl-4 mt-xxl-5">
  <h2 class="h1 text-center pt-2 pt-sm-3 pt-md-4 pt-xl-5 mt-lg-2 mt-xl-1">
    Our services
  </h2>
  <p class="text-center pb-3 mb-3 mb-lg-4">
    With the support of independent referents, you can solve many challenges
  </p>
  <div class="row row-cols-1 row-cols-sm-2 row-cols-lg-3 g-4">
    @for (item of corporateService; track $index) {
      <div class="col">
        <a
          class="card card-hover h-100 border-0 bg-secondary rounded-1 text-decoration-none overflow-hidden"
          href="javascript:void(0);"
        >
          <div
            class="card-body d-flex align-items-end position-absolute top-0 start-0 w-100 h-100 opacity-0 bg-size-cover bg-position-center pb-4"
            style="
            background-image: url({{ item.image }});
          "
          >
            <div
              class="btn btn-icon btn-lg btn-outline-light rounded-circle pe-none mb-3"
            >
              <i class="ai-arrow-right"></i>
            </div>
          </div>
          <div class="card-body pb-3">
            <div [innerHTML]="item.sanitizedIcon"></div>
            <h3 class="h4">{{ item.title }}</h3>
            <p class="mb-0">
              {{ item.description }}
            </p>
          </div>
          <div class="card-footer border-0 pt-3 mb-3">
            <div
              class="btn btn-icon btn-lg btn-outline-primary rounded-circle pe-none"
            >
              <i class="ai-arrow-right"></i>
            </div>
          </div>
        </a>
      </div>
    }
  </div>
</section>
