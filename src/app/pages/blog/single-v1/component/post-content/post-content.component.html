<section class="container py-5 my-md-2 my-lg-3 my-xl-4">
  <div class="row pt-xxl-2">
    <div class="col-lg-9 col-xl-8 pe-lg-4 pe-xl-0">
      <p class="fs-lg">
        Dolor laoreet fermentum lectus praesent aenean molestie mollis integer.
        Sem ullamcorper montes, lorem ullamcorper orci, pellentesque ipsum
        malesuada gravida. Donec imperdiet nulla suscipit in. Dignissim ornare
        ac lorem consectetur massa a. Pellentesque urna pharetra, quis maecenas.
        Sit dolor amet nulla aenean eu, ac. Nisl mi tempus, iaculis viverra
        vestibulum scelerisque imperdiet montes. Mauris massa elit pretium
        elementum eget tortor quis. Semper interdum lectus odio diam.
      </p>
      <p class="fs-lg mb-3">
        Ut pellentesque bibendum dignissim a molestie. Ultrices diam ut vel
        neque tincidunt eget. Sed ut quis sit semper nunc at etiam lacinia. Quam
        laoreet eget id sapien a pharetra, ornare diam dignissim. Lorem amet
        nisl, enim mi aenean mauris. Porta nisl a ultrices ut libero id. Gravida
        a mi neque, tristique justo, et pharetra. Laoreet nulla est nulla cras
        ac arcu sed mattis tristique. Morbi convallis suspendisse enim blandit
        massa. Cursus augue dui mattis morbi velit.
      </p>
      <h2 class="h4 mb-lg-4 pt-3 pt-md-4 pt-xl-5">
        Dolor laoreet fermentum lectus praesent aenean
      </h2>
      <p class="fs-lg pb-4 pb-xl-5">
        Ut pellentesque bibendum dignissim a molestie. Ultrices diam ut vel
        neque tincidunt eget. Sed ut quis sit semper nunc at etiam lacinia. Quam
        laoreet eget id sapien a pharetra, ornare diam dignissim. Lorem amet
        nisl, enim mi aenean mauris. Porta nisl a ultrices ut libero id. Gravida
        a mi neque, tristique justo, et pharetra. Laoreet nulla est nulla cras
        ac arcu sed mattis tristique. Morbi convallis suspendisse enim blandit
        massa. Cursus augue dui mattis morbi velit.
      </p>
      <figure class="figure">
        <img
          class="figure-img rounded-5 mb-3"
          src="assets/img/blog/single/image.jpg"
          alt="Image"
        />
        <figcaption class="figure-caption">
          Image source tristique justo et pharetra
        </figcaption>
      </figure>
      <h2 class="h4 mb-lg-4 pt-3 pt-md-4 pt-xl-5">
        Cursus augue dui mattis morbi velit
      </h2>
      <p class="fs-lg pb-2 pb-lg-0 mb-4 mb-lg-5">
        Proin non congue sem, sed tristique ante. Donec et sollicitudin tellus.
        Duis maximus, dui eget egestas mattis, purus ex tempor nulla, quis
        tempor sapien neque at nisl. Aliquam eu nisi ut nisl ultrices posuere.
        Praesent dignissim efficitur nisi, a hendrerit ipsum elementum sit amet.
        Vivamus non ante nisl. Nunc faucibus velit at eros mollis semper.
        Curabitur aliquam eros tellus, nec facilisis nisl finibus sit amet. Ut
        et dolor nec lorem gravida elementum.
      </p>
      <div class="card border-0 bg-secondary mb-3">
        <div class="card-body">
          <figure>
            <blockquote class="blockquote">
              <p>
                Ut pellentesque bibendum dignissim a molestie ultrices diam ut
                vel neque tincidunt eget sed ut quis sit semper nunc at etiam
                lacinia quam laoreet eget id sapien a pharetra, ornare diam
                dignissim neque tincidunt.
              </p>
            </blockquote>
            <figcaption class="blockquote-footer">Darlene Robertson</figcaption>
          </figure>
        </div>
      </div>
      <h2 class="h4 mb-lg-4 pt-3 pt-md-4 pt-xl-5">
        Lorem ipsum dolor sit amet consectetur
      </h2>
      <p class="fs-lg">
        Ut pellentesque bibendum dignissim a molestie. Ultrices diam ut vel
        neque tincidunt eget. Sed ut quis sit semper nunc at etiam lacinia. Quam
        laoreet eget id sapien a pharetra, ornare diam dignissim.
      </p>
      <ul>
        <li class="mb-1">{{ list }}</li>
      </ul>
      <p class="fs-lg">
        Donec diam neque, efficitur vitae ante a, eleifend placerat est.
        Phasellus dapibus scelerisque diam, eu rhoncus lorem vulputate lobortis.
        Praesent pulvinar venenatis mauris, eget fringilla sem.
      </p>

      <div class="d-flex flex-wrap pt-3 pt-md-4 pt-xl-5 mt-xl-n2">
        <h3 class="h6 py-1 mb-0 me-4">Relevant tags:</h3>
        <a class="nav-link fs-sm py-1 px-0 me-3" href="javasript:void(0);">
          <span class="text-primary">#</span>{{ tag }}
        </a>
      </div>
    </div>

    <aside class="col-lg-3 offset-xl-1 pt-4 pt-lg-0" style="margin-top: -7rem">
      <div
        class="position-sticky top-0 mt-2 mt-md-3 mt-lg-0"
        style="padding-top: 7rem"
      >
        <h4 class="mb-4">Share this post:</h4>
        <div class="d-flex mt-n3 ms-n3 mb-lg-5 mb-4 pb-3 pb-lg-0">
          @for (item of socialICon; track $index) {
            <a
              class="btn btn-outline-secondary btn-icon btn-sm btn-{{
                item
              }} rounded-circle mt-3 ms-3"
              href="javascript:void(0);"
              aria-label="Instagram"
            >
              <i class="ai-{{ item }}"></i>
            </a>
          }
        </div>

        <h4 class="pt-xl-1 mb-4">Relevant topics:</h4>
        <div class="d-flex flex-wrap mt-n3 ms-n3 mb-lg-5 mb-4 pb-3 pb-lg-0">
          <a
            class="btn btn-outline-secondary rounded-pill mt-3 ms-3"
            href="javascript:void(0);"
            >{{ relevant }}</a
          >
        </div>

        <h4 class="pt-xl-1 mb-4">Trending posts:</h4>
        <ul class="list-unstyled mb-0">
          @for (item of trendingPostData; track $index; let last = $last) {
            <li [ngClass]="{ 'border-bottom pb-3 mb-3': !last }">
              <span class="h6 mb-0">
                <a routerLink="{{ item.link }}">{{ item.title }}</a>
              </span>
            </li>
          }
        </ul>
      </div>
    </aside>
  </div>
</section>
