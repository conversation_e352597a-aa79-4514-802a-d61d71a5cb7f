<section class="container pt-2 pt-sm-3 pb-5 mb-md-3 mb-lg-4 mb-xl-5">
  <div class="d-flex align-items-center pb-3 mb-3 mb-lg-4">
    <h2 class="h1 mb-0 me-4">Related articles</h2>
    <div class="d-flex ms-auto">
      <button
        class="btn btn-prev btn-icon btn-sm btn-outline-primary rounded-circle me-3"
        type="button"
        id="prev-post"
        aria-label="Prev"
      >
        <i class="ai-arrow-left"></i>
      </button>
      <button
        class="btn btn-next btn-icon btn-sm btn-outline-primary rounded-circle"
        type="button"
        id="next-post"
        aria-label="Next"
      >
        <i class="ai-arrow-right"></i>
      </button>
    </div>
  </div>
  <div class="swiper pb-2 pb-sm-3 pb-md-4">
    <swiper-container
      class="swiper-wrapper"
      [config]="swiperConfig"
      init="false"
    >
      @for (item of swiper; track $index) {
        <swiper-slide class="swiper-slide">
          <div class="position-relative">
            <img class="rounded-5" src="{{ item.image }}" alt="Post image" />
            <h3 class="h4 mt-4 mb-0">
              <a class="stretched-link" routerLink="{{ item.link }}">{{
                item.title
              }}</a>
            </h3>
          </div>
        </swiper-slide>
      }
    </swiper-container>
  </div>
</section>
