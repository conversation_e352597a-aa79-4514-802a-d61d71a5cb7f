import { Component } from '@angular/core'
import { Breadcrumb2Component } from '@components/breadcrumb/breadcrumb-2/breadcrumb-2.component'
import { NavigationBar2Component } from '@components/navigation-bars'
import { BlogSingleArticleComponent } from './component/article/article.component'
import { BlogSingleSubscriptionComponent } from './component/subscription/subscription.component'
import { BlogSingleFooterComponent } from './component/footer/footer.component'
import { BlogSinglePostContentComponent } from './component/post-content/post-content.component'

@Component({
  selector: 'blog-single',
  standalone: true,
  imports: [
    NavigationBar2Component,
    Breadcrumb2Component,
    BlogSingleArticleComponent,
    BlogSingleSubscriptionComponent,
    BlogSingleFooterComponent,
    BlogSinglePostContentComponent,
  ],
  templateUrl: './blog-single.component.html',
  styles: ``,
})
export class BlogSingle {}
