import { Component, inject } from '@angular/core'
import { TrendingType, trendingPost } from '../../../grid-sidebar/data'
import { CategoryType } from '../../data'
import { CommonModule } from '@angular/common'
import { NgbActiveOffcanvas } from '@ng-bootstrap/ng-bootstrap'
import { TagService } from 'src/app/services/tag.service'
import { TagDataItem, TagDataResponse } from 'src/app/models/tag.model'

@Component({
  selector: 'list-sidebar-category',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './category.component.html',
  styles: ``,
})
export class CategoryComponent {
  TagSer: any

  activeOffcanvas = inject(NgbActiveOffcanvas)
  tagService = inject(TagService)

  trendingPostData: TrendingType[] = trendingPost
  // categoryData: CategoryType[] = category
  tagData: TagDataItem[] = []

  ngOnInit() {
    this.fetchTag()
    console.log("After call", this.tagData)
  }

  fetchTag() {
    this.tagService.getAll().subscribe({
      next: (response) => {
        this.tagData = response.data
      },
      error: (error) => {
        console.log(error)
      },
    })
  }
}
