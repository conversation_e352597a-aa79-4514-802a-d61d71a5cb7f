<ngx-masonry class="masonry-grid mb-2 mb-md-4 pb-lg-3" data-columns="3">
  @for (item of allBlogPost; track $index) {
    <article class="masonry-grid-item" ngxMasonryItem>
      @if (item.image) {
        <div class="card border-0 bg-secondary">
          <a routerLink="blog/single-v2">
            <img class="card-img-top" src="{{ item.image }}" alt="Post image" />
          </a>
          <div class="card-body pb-4">
            <div class="d-flex align-items-center mb-4 mt-n1">
              <span class="fs-sm text-body-secondary">{{ item.date }}</span>
              <span class="fs-xs opacity-20 mx-3">|</span>
              <a
                class="badge text-nav fs-xs border"
                href="javascript:void(0);"
                >{{ item.category }}</a
              >
            </div>
            <h3 class="h4 card-title">
              <a routerLink="blog/single-v1">{{ item.title }}</a>
            </h3>
            <p class="card-text">{{ item.content }}</p>
          </div>
          <div class="card-footer pt-3">
            <a
              class="d-flex align-items-center text-decoration-none pb-2"
              href="javascript:void(0);"
            >
              <img
                class="rounded-circle"
                src="{{ item.author.avatar }}"
                width="48"
                alt="Post author"
              />
              <h6 class="ps-3 mb-0">{{ item.author.name }}</h6>
            </a>
          </div>
        </div>
      } @else {
        <div class="card border-0 bg-secondary">
          <div class="card-body pb-4">
            <div class="d-flex align-items-center mb-4 mt-n1">
              <span class="fs-sm text-body-secondary">{{ item.date }}</span>
              <span class="fs-xs opacity-20 mx-3">|</span>
              <a
                class="badge text-nav fs-xs border"
                href="javascript:void(0);"
                >{{ item.category }}</a
              >
            </div>
            <h3 class="h4 card-title">
              <a routerLink="blog/single-v1">{{ item.title }}</a>
            </h3>
            <p class="card-text">{{ item.content }}</p>
          </div>
          <div class="card-footer pt-3">
            <a
              class="d-flex align-items-center text-decoration-none pb-2"
              href="javascript:void(0);"
            >
              <img
                class="rounded-circle"
                src="{{ item.author.avatar }}"
                width="48"
                alt="Post author"
              />
              <h6 class="ps-3 mb-0">{{ item.author.name }}</h6>
            </a>
          </div>
        </div>
      }
    </article>
  }
</ngx-masonry>

<div class="row gy-3 align-items-center mb-md-2 mb-xl-4">
  <div class="col col-md-4 col-6 order-md-1 order-1">
    <div class="d-flex align-items-center">
      <span class="text-body-secondary fs-sm">Show</span>
      <select class="form-select form-select-flush w-auto">
        <option value="6">6</option>
        <option value="9" selected>9</option>
        <option value="12">12</option>
        <option value="24">24</option>
      </select>
    </div>
  </div>
  <div class="col col-md-4 col-12 order-md-2 order-3 text-center">
    <button class="btn btn-primary w-md-auto w-100" type="button">
      Load more posts
    </button>
  </div>
  <div class="col col-md-4 col-6 order-md-3 order-2">
    <nav aria-label="Page navigation">
      <ngb-pagination
        [collectionSize]="50"
        [directionLinks]="false"
        class="pagination pagination-sm justify-content-end"
      >
        <ng-template class="page-item active" aria-current="page">
          <span class="page-link"
            >1<span class="visually-hidden">(current)</span></span
          >
        </ng-template>
        <ng-template class="page-item"
          ><a class="page-link" href="javascript:void(0);">2</a></ng-template
        >
        <ng-template class="page-item"
          ><a class="page-link" href="javascript:void(0);">3</a></ng-template
        >
        <ng-template class="page-item"
          ><a class="page-link" href="javascript:void(0);">4</a></ng-template
        >
        <ng-template class="page-item"
          ><a class="page-link" href="javascript:void(0);">5</a></ng-template
        >
      </ngb-pagination>
    </nav>
  </div>
</div>
