<auth-layout>
  <div
    class="d-flex flex-column align-items-center w-lg-50 h-100 px-3 px-lg-5 pt-5"
  >
    <div class="w-100 mt-auto" style="max-width: 526px">
      <h1>No account? Sign up</h1>
      <p class="pb-3 mb-3 mb-lg-4">
        Have an account already?&nbsp;&nbsp;<a routerLink="/auth/sign-in"
          >Sign in here!</a
        >
      </p>
      <form
        class="needs-validation"
        (ngSubmit)="signup()"
        [formGroup]="signupForm"
        novalidate
      >
        <div class="row row-cols-1 row-cols-sm-2">
          <div class="col mb-4">
            <input
              class="form-control form-control-lg"
              formControlName="fname"
              type="text"
              placeholder="Your name"
              required
            />
          </div>
          <div class="col mb-4">
            <input
              class="form-control form-control-lg"
              formControlName="email"
              type="email"
              placeholder="Email address"
              required
            />
          </div>
        </div>
        <div class="password-toggle mb-4">
          <input
            class="form-control form-control-lg"
            [type]="passwordType"
            formControlName="password"
            type="password"
            placeholder="Password"
            required
          />
          <label class="password-toggle-btn" aria-label="Show/hide password">
            <input
              class="password-toggle-check"
              type="checkbox"
              (change)="changePasswordType($event)"
            />
            <span class="password-toggle-indicator"></span>
          </label>
        </div>
        <div class="password-toggle mb-4">
          <input
            class="form-control form-control-lg"
            [type]="confirmpasswordType"
            formControlName="confirmpwd"
            type="password"
            placeholder="Confirm password"
            required
          />
          <label class="password-toggle-btn" aria-label="Show/hide password">
            <input
              class="password-toggle-check"
              type="checkbox"
              (change)="changeconfirmPasswordType($event)"
            />
            <span class="password-toggle-indicator"></span>
          </label>
        </div>
        <div class="pb-4">
          <div class="form-check my-2">
            <input class="form-check-input" type="checkbox" id="terms" />
            <label class="form-check-label ms-1" for="terms"
              >I agree to
              <a href="javascript:void(0);">Terms &amp; Conditions</a></label
            >
          </div>
        </div>
        <button class="btn btn-lg btn-primary w-100 mb-4" type="submit">
          Sign up
        </button>

        <h2 class="h6 text-center pt-3 pt-lg-4 mb-4">
          Or sign in with your social account
        </h2>
        <div class="row row-cols-1 row-cols-sm-2 gy-3">
          <div class="col">
            <a
              class="btn btn-icon btn-outline-secondary btn-google btn-lg w-100"
              href="javascript:void(0);"
            >
              <i class="ai-google fs-xl me-2"></i>
              Google
            </a>
          </div>
          <div class="col">
            <a
              class="btn btn-icon btn-outline-secondary btn-facebook btn-lg w-100"
              href="javascript:void(0);"
            >
              <i class="ai-facebook fs-xl me-2"></i>
              Facebook
            </a>
          </div>
        </div>
      </form>
    </div>

    <p class="nav w-100 fs-sm pt-5 mt-auto mb-5" style="max-width: 526px">
      <span class="text-body-secondary"
        >&copy; All rights reserved. Made by</span
      ><a
        class="nav-link d-inline-block p-0 ms-1"
        href="{{ developBy }}"
        target="_blank"
        rel="noopener"
        >{{ author }}</a
      >
    </p>
  </div>
</auth-layout>
