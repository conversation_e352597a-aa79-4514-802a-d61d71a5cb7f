<h1 class="h2 mb-4">Billing</h1>

<section class="card border-0 py-1 p-md-2 p-xl-3 p-xxl-4 mb-4">
  <div class="card-body">
    <div class="d-flex align-items-center mt-sm-n1 pb-4 mb-0 mb-lg-1 mb-xl-3">
      <i class="ai-card text-primary lead pe-1 me-2"></i>
      <h2 class="h4 mb-0">Payment methods</h2>
    </div>
    <div class="alert alert-danger d-flex mb-4">
      <i class="ai-octagon-alert fs-xl me-2"></i>
      <p class="mb-0">
        Your primary credit card expired on 01/04/{{ currentYear }}. Please add
        a new card or update this one.
      </p>
    </div>
    <div class="row row-cols-1 row-cols-md-2 g-4">
      @for (item of allPaymentMethod; track $index) {
        <div class="col">
          <div class="card h-100 rounded-3 p-3 p-sm-4">
            <div class="d-flex align-items-center pb-2 mb-1">
              <h3 class="h6 text-nowrap text-truncate mb-0">{{ item.name }}</h3>
              <span
                class="badge bg-primary bg-opacity-10 text-primary fs-xs ms-3"
                >{{ item.status }}</span
              >
              <div class="d-flex ms-auto">
                <button
                  class="nav-link fs-xl fw-normal py-1 pe-0 ps-1 ms-2"
                  type="button"
                  ngbTooltip="Edit"
                  data-bs-toggle="tooltip"
                  aria-label="Edit"
                >
                  <i class="ai-edit-alt"></i>
                </button>
                <button
                  class="nav-link text-danger fs-xl fw-normal py-1 pe-0 ps-1 ms-2"
                  type="button"
                  ngbTooltip="Delete"
                  data-bs-toggle="tooltip"
                  aria-label="Delete"
                >
                  <i class="ai-trash"></i>
                </button>
              </div>
            </div>
            <div class="d-flex align-items-center">
              <div [innerHTML]="item.sanitizedIcon"></div>
              <div class="fs-sm" [ngClass]="item.description ? 'ps-2' : 'ps-3'">
                @if (item.description) {
                  <div class="text-dark">{{ item.description }}</div>
                  <div class="text-body-secondary">{{ item.email }}</div>
                } @else {
                  <div class="text-dark">
                    {{ item.card_type }} •••• {{ item.last_four_digits }}
                  </div>
                  <div class="text-body-secondary">{{ item.expiration }}</div>
                }
              </div>
            </div>
          </div>
        </div>
      }

      <div class="col">
        <div
          class="card h-100 justify-content-center align-items-center border-dashed rounded-3 py-5 px-3 px-sm-4"
        >
          <a
            class="stretched-link d-flex align-items-center fw-semibold text-decoration-none"
            (click)="openPaymentModal(econtent)"
            href="javascript:void(0);"
            data-bs-toggle="modal"
          >
            <i class="ai-circle-plus fs-xl me-2"></i>
            Add new payment methods
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="card border-0 py-1 p-md-2 p-xl-3 p-xxl-4">
  <div class="card-body">
    <div class="d-flex align-items-center pb-4 mt-sm-n1 mb-0 mb-lg-1 mb-xl-3">
      <i class="ai-map-pin text-primary lead pe-1 me-2"></i>
      <h2 class="h4 mb-0">Billing address</h2>
    </div>
    <div class="row row-cols-1 row-cols-md-2 g-4">
      @for (item of allAdresses; track $index) {
        <div class="col">
          <div class="card h-100 rounded-3 p-3 p-sm-4">
            <div class="d-flex align-items-center pb-2 mb-1">
              <h3 class="h6 text-nowrap text-truncate mb-0">{{ item.type }}</h3>
              @if (item.primary) {
                <span
                  class="badge bg-primary bg-opacity-10 text-primary fs-xs ms-3"
                  >{{ item.primary }}</span
                >
              }
              <div class="d-flex ms-auto">
                <button
                  class="nav-link fs-xl fw-normal py-1 pe-0 ps-1 ms-2"
                  type="button"
                  data-bs-toggle="tooltip"
                  title="Edit"
                  aria-label="Edit"
                >
                  <i class="ai-edit-alt"></i>
                </button>
                <button
                  class="nav-link text-danger fs-xl fw-normal py-1 pe-0 ps-1 ms-2"
                  type="button"
                  data-bs-toggle="tooltip"
                  title="Delete"
                  aria-label="Trash"
                >
                  <i class="ai-trash"></i>
                </button>
              </div>
            </div>
            <p class="mb-0">
              {{ item.address }},<br />{{ item.city }}, {{ item.state }}
              {{ item.postal_code }},<br />{{ item.country }}
            </p>
          </div>
        </div>
      }

      <div class="col">
        <div
          class="card h-100 justify-content-center align-items-center border-dashed rounded-3 py-5 px-3 px-sm-4"
        >
          <a
            class="stretched-link d-flex align-items-center fw-semibold text-decoration-none my-sm-3"
            (click)="openModal(content)"
            href="javascript:void(0);"
            data-bs-toggle="modal"
          >
            <i class="ai-circle-plus fs-xl me-2"></i>
            Add new address
          </a>
        </div>
      </div>
    </div>
    <div class="py-4 mt-sm-2 mt-md-3">
      <h3 class="h6 mb-1">Tax location</h3>
      <p class="mb-0">United States - 10% VAT</p>
    </div>
    <div class="alert alert-info d-flex mb-0">
      <i class="ai-circle-info fs-xl me-2"></i>
      <p class="mb-0">
        Your text location determines the taxes that are applied to your bill.
        <a href="javascript:void(0);" class="alert-link">Learn more</a>
      </p>
    </div>
  </div>
</section>

<ng-template
  let-modal
  id="addAddress"
  #content
  data-bs-backdrop="static"
  tabindex="-1"
  role="dialog"
>
  <div class="modal-header border-0">
    <h4 class="modal-title">Add new address</h4>
    <button
      class="btn-close"
      type="button"
      data-bs-dismiss="modal"
      aria-label="Close"
      (click)="modal.dismiss('Cross click')"
    ></button>
  </div>
  <form
    class="modal-body needs-validation pt-0"
    novalidate
    (ngSubmit)="addBilling()"
    [formGroup]="adressForm"
  >
    <div class="alert alert-warning d-flex mb-4">
      <i class="ai-triangle-alert fs-xl me-2"></i>
      <p class="mb-0">
        Updating your address may affect your
        <a href="javascript:void(0);" class="alert-link">Tax Location</a>
      </p>
    </div>
    <div class="row row-cols-1 row-cols-lg-2 g-4 pb-2 pb-sm-3 mb-4">
      <div class="col">
        <label class="form-label" for="country">Country</label>
        <select
          class="form-select"
          required
          id="country"
          formControlName="country"
        >
          <option value="" disabled selected>Select a country</option>
          <option value="Australia">Australia</option>
          <option value="Belgium">Belgium</option>
          <option value="Canada">Canada</option>
          <option value="Denmark">Denmark</option>
          <option value="USA">USA</option>
        </select>
      </div>
      <div class="col">
        <label class="form-label" for="city">City</label>
        <select class="form-select" required id="city" formControlName="city">
          <option value="" disabled selected>Select a city</option>
          <option value="Sydney">Sydney</option>
          <option value="Brussels">Brussels</option>
          <option value="Toronto">Toronto</option>
          <option value="Copenhagen">Copenhagen</option>
          <option value="New York">New York</option>
        </select>
      </div>
      <div class="col">
        <label class="form-label" for="state">State</label>
        <select class="form-select" required id="state" formControlName="state">
          <option value="" disabled selected>Select a state</option>
          <option value="Arizona">Arizona</option>
          <option value="California">California</option>
          <option value="Florida">Florida</option>
          <option value="Georgia">Georgia</option>
          <option value="Texas">Texas</option>
          <option value="Virginia">Virginia</option>
        </select>
      </div>
      <div class="col">
        <label class="form-label" for="address1">Address line 1</label>
        <input
          class="form-control"
          type="text"
          required
          id="address1"
          formControlName="adreess1"
        />
      </div>
      <div class="col">
        <label class="form-label" for="address2">Address line 2</label>
        <input
          class="form-control"
          type="text"
          id="address2"
          formControlName="adress2"
        />
      </div>
      <div class="col">
        <label class="form-label" for="postcode">Post code</label>
        <input
          class="form-control"
          type="text"
          formControlName="code"
          mask="000-0000"
          data-format='{"delimiter": "-", "blocks": [3, 4], "uppercase": true}'
          placeholder="XXX-XXXX"
          id="postcode"
        />
      </div>
      <div class="col">
        <div class="form-check">
          <input class="form-check-input" type="checkbox" id="set-primary" />
          <label class="form-check-label text-dark fw-medium" for="set-primary"
            >Set as primary billing address</label
          >
        </div>
      </div>
    </div>
    <div class="d-flex flex-column flex-sm-row">
      <button
        (click)="modal.dismiss('Cross click')"
        class="btn btn-secondary mb-3 mb-sm-0"
        type="reset"
        data-bs-dismiss="modal"
      >
        Cancel
      </button>
      <button [type]="'submit'" class="btn btn-primary ms-sm-3" type="submit">
        Save address
      </button>
    </div>
  </form>
</ng-template>

<ng-template
  let-modal
  #econtent
  class="modal fade"
  id="addCard"
  data-bs-backdrop="static"
  tabindex="-1"
  role="dialog"
>
  <div class="modal-header border-0">
    <h4 class="modal-title">Add new card</h4>
    <button
      class="btn-close"
      type="button"
      data-bs-dismiss="modal"
      aria-label="Close"
      (click)="modal.dismiss('Cross click')"
    ></button>
  </div>
  <form
    class="modal-body needs-validation pt-0"
    novalidate
    [formGroup]="paymentForm"
    (ngSubmit)="addPayment()"
  >
    <div class="mb-4">
      <label class="form-label" for="card-name">Name on card</label>
      <input
        class="form-control"
        type="text"
        formControlName="name"
        placeholder="John Doe"
        required
        id="card-name"
      />
    </div>
    <div class="mb-4">
      <label class="form-label" for="card-number">Card number</label>
      <div class="input-group">
        <input
          class="form-control"
          type="text"
          formControlName="last_four_digits"
          mask="0000 0000 0000 0000"
          placeholder="XXXX XXXX XXXX XXXX"
          required
          id="card-number"
        />
        <div class="input-group-text py-0">
          <div class="credit-card-icon"></div>
        </div>
      </div>
    </div>
    <div class="row row-cols-2 g-4 pb-2 pb-sm-3 mb-4">
      <div class="col">
        <label class="form-label" for="card-expiration">Expiration date</label>
        <input
          class="form-control"
          type="text"
          formControlName="expiration"
          mask="00/00"
          data-format='{"date": true, "datePattern": ["m", "y"]}'
          placeholder="MM/YY"
          required
          id="card-expiration"
        />
      </div>
      <div class="col">
        <label class="form-label" for="card-cvv">CVV Code</label>
        <input
          class="form-control"
          type="text"
          formControlName="cvv"
          mask="000"
          data-format='{"numericOnly": true, "blocks": [3]}'
          placeholder="XXX"
          required
          id="card-cvv"
        />
      </div>
    </div>
    <div class="d-flex flex-column flex-sm-row">
      <button
        (click)="modal.dismiss('Cross click')"
        class="btn btn-secondary mb-3 mb-sm-0"
        type="reset"
        data-bs-dismiss="modal"
      >
        Cancel
      </button>
      <button [type]="'submit'" class="btn btn-primary ms-sm-3" type="submit">
        Save new card
      </button>
    </div>
  </form>
</ng-template>
