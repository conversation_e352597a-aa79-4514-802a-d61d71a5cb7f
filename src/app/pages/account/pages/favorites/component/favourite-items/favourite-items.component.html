<div class="card border-0 py-1 p-md-2 p-xl-3 p-xxl-4">
  <div class="card-body pb-4">
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-4">
      @for (item of allFavouriteItem; track $index) {
        <div class="col pb-2 pb-sm-3">
          <div
            class="card-hover position-relative bg-secondary rounded-1 p-3 mb-4"
          >
            @if (item.badge) {
              <span
                class="badge bg-opacity-10 position-absolute top-0 start-0 mt-3 ms-3"
                [ngClass]="{
                  'bg-success text-success': item.badge === 'New',
                  'bg-danger text-danger': item.badge === 'Sale',
                  'bg-gray text-nav ': item.badge === 'Out Of Stock',
                }"
                >{{ item.badge }}</span
              >
            }
            <button
              class="btn btn-icon btn-sm btn-light bg-light border-0 rounded-circle position-absolute top-0 end-0 mt-3 me-3 z-5 opacity-0"
              type="button"
              aria-label="Remove"
            >
              <i class="ai-trash fs-xl text-danger"></i>
            </button>
            <div
              class="swiper swiper-nav-onhover"
              data-swiper-options='{"loop": true, "navigation": {"prevEl": ".btn-prev", "nextEl": ".btn-next"}}'
            >
              <a class="swiper-wrapper" routerLink="/shop/product">
                <div class="swiper-slide p-2 p-xl-4">
                  <img
                    class="d-block mx-auto"
                    src="{{ item.image_src }}"
                    width="226"
                    alt="Product"
                  />
                </div>
              </a>
              <button
                class="btn btn-prev btn-icon btn-sm btn-light bg-light border-0 rounded-circle start-0"
                type="button"
                aria-label="Prev"
              >
                <i class="ai-chevron-left fs-xl text-nav"></i>
              </button>
              <button
                class="btn btn-next btn-icon btn-sm btn-light bg-light border-0 rounded-circle end-0"
                type="button"
                aria-label="Next"
              >
                <i class="ai-chevron-right fs-xl text-nav"></i>
              </button>
            </div>
          </div>
          <div class="d-flex mb-1">
            <h3 class="h6 mb-0">
              <a routerLink="/shop/product">{{ item.name }}</a>
            </h3>
            <div class="d-flex ps-2 mt-n1 ms-auto">
              @for (data of item.colors; track data.name; let i = $index) {
                <div class="ms-1">
                  <input
                    class="btn-check"
                    type="radio"
                    name="color{{ item.id }}"
                    value="{{ data.name }}"
                    checked
                    id="color{{ item.id }}-{{ i }}"
                  />
                  <label
                    class="btn btn-icon btn-xs btn-outline-secondary rounded-circle"
                    for="color{{ item.id }}-{{ i }}"
                  >
                    <span
                      class="d-block rounded-circle"
                      style="
                        width: 0.625rem;
                        height: 0.625rem;
                        background-color: #576071;
                      "
                      [style.background-color]="data.hex"
                      [style.background-image]="data.backgroundImage"
                    ></span>
                  </label>
                </div>
              }
            </div>
          </div>
          <div class="d-flex align-items-center">
            <span class="me-2">{{ selectedCurrency }}{{ item.price }}</span>
            @if (item.discounted_price) {
              <del class="fs-sm text-body-secondary"
                >{{ selectedCurrency }}{{ item.discounted_price }}</del
              >
            }
            <div
              class="nav ms-auto"
              data-bs-toggle="tooltip"
              data-bs-template="<div class='tooltip fs-xs' role='tooltip'><div class='tooltip-inner bg-light text-body-secondary p-0'></div></div>"
              data-bs-placement="left"
              ngbTooltip="Add to cart"
            >
              <a
                class="nav-link fs-lg py-2 px-1"
                href="javascript:void(0);"
                aria-label="Add to Cart"
              >
                <i class="ai-cart"></i>
              </a>
            </div>
          </div>
        </div>
      }
    </div>
  </div>
</div>
