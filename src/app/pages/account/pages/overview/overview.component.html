<h1 class="h2 mb-4">Overview</h1>

<section class="card border-0 py-1 p-md-2 p-xl-3 p-xxl-4 mb-4">
  <div class="card-body">
    <div class="d-flex align-items-center mt-sm-n1 pb-4 mb-0 mb-lg-1 mb-xl-3">
      <i class="ai-user text-primary lead pe-1 me-2"></i>
      <h2 class="h4 mb-0">Basic info</h2>
      <a
        class="btn btn-sm btn-secondary ms-auto"
        routerLink="/account/settings"
      >
        <i class="ai-edit ms-n1 me-2"></i>
        Edit info
      </a>
    </div>
    <div class="d-md-flex align-items-center">
      <div class="d-sm-flex align-items-center">
        <div
          class="rounded-circle bg-size-cover bg-position-center flex-shrink-0"
          style="
            width: 80px;
            height: 80px;
            background-image: url(assets/img/avatar/02.jpg);
          "
        ></div>
        <div class="pt-3 pt-sm-0 ps-sm-3">
          <h3 class="h5 mb-2">
            Isabella Bocouse<i
              class="ai-circle-check-filled fs-base text-success ms-2"
            ></i>
          </h3>
          <div
            class="text-body-secondary fw-medium d-flex flex-wrap flex-sm-nowrap align-iteems-center"
          >
            <div class="d-flex align-items-center me-3">
              <i class="ai-mail me-1"></i>
              email&#64;example.com
            </div>
            <div class="d-flex align-items-center text-nowrap">
              <i class="ai-map-pin me-1"></i>
              USA, {{ selectedCurrency }}
            </div>
          </div>
        </div>
      </div>
      <div class="w-100 pt-3 pt-md-0 ms-md-auto" style="max-width: 212px">
        <div class="d-flex justify-content-between fs-sm pb-1 mb-2">
          Profile completion
          <strong class="ms-2">62%</strong>
        </div>
        <div class="progress" style="height: 5px">
          <div
            class="progress-bar"
            role="progressbar"
            style="width: 62%"
            aria-valuenow="62"
            aria-valuemin="0"
            aria-valuemax="100"
          ></div>
        </div>
      </div>
    </div>
    <div class="row py-4 mb-2 mb-sm-3">
      <div class="col-md-6 mb-4 mb-md-0">
        <table class="table mb-0">
          <tbody>
            @for (item of attrubutes; track $index) {
              <tr>
                <td class="border-0 text-body-secondary py-1 px-0">
                  {{ item.attribute }}
                </td>
                <td class="border-0 text-dark fw-medium py-1 ps-3">
                  {{ item.value }}
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
      <div class="col-md-6 d-md-flex justify-content-end">
        <div class="w-100 border rounded-3 p-4" style="max-width: 212px">
          <img
            class="d-block mb-2"
            src="assets/img/account/gift-icon.svg"
            width="24"
            alt="Gift icon"
          />
          <h4 class="h5 lh-base mb-0">123 bonuses</h4>
          <p class="fs-sm text-body-secondary mb-0">
            1 bonus = {{ selectedCurrency }}1
          </p>
        </div>
      </div>
    </div>
    <div class="alert alert-info d-flex mb-0" role="alert">
      <i class="ai-circle-info fs-xl"></i>
      <div class="ps-2">
        Fill in the information 100% to receive more suitable offers.<a
          class="alert-link ms-1"
          routerLink="/account/settings"
          >Go to settings!</a
        >
      </div>
    </div>
  </div>
</section>

<div class="row row-cols-1 row-cols-md-2 g-4 mb-4">
  <section class="col">
    <div class="card h-100 border-0 py-1 p-md-2 p-xl-3 p-xxl-4">
      <div class="card-body">
        <div class="d-flex align-items-center mt-sm-n1 pb-4 mb-1 mb-lg-2">
          <i class="ai-map-pin text-primary lead pe-1 me-2"></i>
          <h2 class="h4 mb-0">Address</h2>
          <a
            class="btn btn-sm btn-secondary ms-auto"
            routerLink="/account/settings"
          >
            <i class="ai-edit ms-n1 me-2"></i>
            Edit info
          </a>
        </div>
        <div class="d-flex align-items-center pb-1 mb-2">
          <h3 class="h6 mb-0 me-3">Shipping address</h3>
          <span class="badge bg-primary bg-opacity-10 text-primary"
            >Primary</span
          >
        </div>
        <p class="mb-0">
          401 Magnetic Drive Unit 2,<br />Toronto, Ontario, M3J 3H9<br />Canada
        </p>
        <div class="d-flex align-items-center pt-4 pb-1 my-2">
          <h3 class="h6 mb-0 me-3">Billing address 1</h3>
          <span class="badge bg-primary bg-opacity-10 text-primary"
            >Primary</span
          >
        </div>
        <p class="mb-0">
          314 Robinson Lane,<br />Wilmington, DE 19805,<br />USA
        </p>
      </div>
    </div>
  </section>

  <section class="col">
    <div class="card h-100 border-0 py-1 p-md-2 p-xl-3 p-xxl-4">
      <div class="card-body">
        <div class="d-flex align-items-center mt-sm-n1 pb-4 mb-1 mb-lg-2">
          <i class="ai-wallet text-primary lead pe-1 me-2"></i>
          <h2 class="h4 mb-0">Billing</h2>
          <a
            class="btn btn-sm btn-secondary ms-auto"
            routerLink="/account/billing"
          >
            <i class="ai-edit ms-n1 me-2"></i>
            Edit info
          </a>
        </div>
        <div class="d-flex align-items-center pb-1 mb-2">
          <h3 class="h6 mb-0 me-3">Isabella Bocouse</h3>
          <span class="badge bg-primary bg-opacity-10 text-primary"
            >Primary</span
          >
        </div>
        <div class="d-flex align-items-center pb-4 mb-2 mb-sm-3">
          <svg
            width="52"
            height="42"
            viewBox="0 0 52 42"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22.6402 28.2865H18.5199L21.095 12.7244H25.2157L22.6402 28.2865ZM15.0536 12.7244L11.1255 23.4281L10.6607 21.1232L10.6611 21.124L9.27467 14.1256C9.27467 14.1256 9.10703 12.7244 7.32014 12.7244H0.8262L0.75 12.9879C0.75 12.9879 2.73586 13.3942 5.05996 14.7666L8.63967 28.2869H12.9327L19.488 12.7244H15.0536ZM47.4619 28.2865H51.2453L47.9466 12.7239H44.6345C43.105 12.7239 42.7324 13.8837 42.7324 13.8837L36.5873 28.2865H40.8825L41.7414 25.9749H46.9793L47.4619 28.2865ZM42.928 22.7817L45.093 16.9579L46.3109 22.7817H42.928ZM36.9095 16.4667L37.4975 13.1248C37.4975 13.1248 35.6831 12.4463 33.7916 12.4463C31.7469 12.4463 26.8913 13.3251 26.8913 17.5982C26.8913 21.6186 32.5902 21.6685 32.5902 23.7803C32.5902 25.8921 27.4785 25.5137 25.7915 24.182L25.1789 27.6763C25.1789 27.6763 27.0187 28.555 29.8296 28.555C32.6414 28.555 36.8832 27.1234 36.8832 23.2271C36.8832 19.1808 31.1331 18.8041 31.1331 17.0449C31.1335 15.2853 35.1463 15.5113 36.9095 16.4667Z"
              fill="#2566AF"
            />
            <path
              d="M10.6611 22.1235L9.2747 15.1251C9.2747 15.1251 9.10705 13.7239 7.32016 13.7239H0.8262L0.75 13.9874C0.75 13.9874 3.87125 14.6235 6.86507 17.0066C9.72766 19.2845 10.6611 22.1235 10.6611 22.1235Z"
              fill="#E6A540"
            />
          </svg>
          <div class="ps-3 fs-sm">
            <div class="text-dark">Visa •••• 9016</div>
            <div class="text-body-secondary">Debit - Expires 03/24</div>
          </div>
        </div>
        <div class="alert alert-danger d-flex mb-0">
          <i class="ai-octagon-alert fs-xl me-2"></i>
          <p class="mb-0">
            Your primary credit card expired on 01/04/{{ currentYear }}. Please
            add a new card or update this one.
          </p>
        </div>
      </div>
    </div>
  </section>
</div>

<section class="card border-0 py-1 p-md-2 p-xl-3 p-xxl-4">
  <div class="card-body">
    <div class="d-flex align-items-center mt-sm-n1 pb-4 mb-0 mb-lg-1 mb-xl-3">
      <i class="ai-cart text-primary lead pe-1 me-2"></i>
      <h2 class="h4 mb-0">Orders</h2>
      <a class="btn btn-sm btn-secondary ms-auto" routerLink="/account/orders"
        >View all</a
      >
    </div>

    <div
      ngbAccordion
      class="accordion accordion-alt accordion-orders"
      id="orders"
      [closeOthers]="true"
    >
      @for (item of allOrder; track $index) {
        <div class="accordion-item border-top mb-0" ngbAccordionItem>
          <h4 class="accordion-header" ngbAccordionHeader>
            <button
              ngbAccordionButton
              class="accordion-button d-flex fs-4 fw-normal text-decoration-none py-3"
            >
              <div
                class="d-flex justify-content-between w-100"
                style="max-width: 440px"
              >
                <div class="me-3 me-sm-4">
                  <div class="fs-sm text-body-secondary">
                    {{ item.orderNumber }}
                  </div>
                  <span
                    class="badge bg-opacity-10 fs-xs"
                    [ngClass]="{
                      'bg-info text-info': item.status === 'In progress',
                      'bg-danger text-danger': item.status === 'Canceled',
                      'bg-primary text-primary': item.status === 'Delivered',
                    }"
                    >{{ item.status }}</span
                  >
                </div>
                <div class="me-3 me-sm-4">
                  <div class="d-none d-sm-block fs-sm text-body-secondary mb-2">
                    Order date
                  </div>
                  <div class="d-sm-none fs-sm text-body-secondary mb-2">
                    Date
                  </div>
                  <div class="fs-sm fw-medium text-dark">
                    {{ item.orderDate }}
                  </div>
                </div>
                <div class="me-3 me-sm-4">
                  <div class="fs-sm text-body-secondary mb-2">Total</div>
                  <div class="fs-sm fw-medium text-dark">
                    {{ selectedCurrency }}{{ item.total }}
                  </div>
                </div>
              </div>
              <div class="accordion-button-img d-none d-sm-flex ms-auto">
                @for (item of item.images; track $index) {
                  <div class="mx-1">
                    <img src="{{ item }}" width="48" alt="Product" />
                  </div>
                }
              </div>
            </button>
          </h4>
          <div class="accordion-collapse" ngbAccordionCollapse>
            <div class="accordion-body" ngbAccordionBody>
              <ng-template>
                <div class="table-responsive pt-1">
                  <table
                    class="table align-middle w-100"
                    style="min-width: 450px"
                  >
                    <tbody>
                      @for (data of item.products; track $index) {
                        <tr>
                          <td class="border-0 py-1 px-0">
                            <div class="d-flex align-items-center">
                              <a
                                class="d-inline-block flex-shrink-0 bg-secondary rounded-1 p-md-2 p-lg-3"
                                routerLink="/shop/single"
                              >
                                <img
                                  src="{{ data.image }}"
                                  width="110"
                                  alt="Product"
                                />
                              </a>
                              <div class="ps-3 ps-sm-4">
                                <h4 class="h6 mb-2">
                                  <a routerLink="/shop/single">{{
                                    data.name
                                  }}</a>
                                </h4>
                                <div class="text-body-secondary fs-sm me-3">
                                  Color:
                                  <span class="text-dark fw-medium">{{
                                    data.color
                                  }}</span>
                                </div>
                              </div>
                            </div>
                          </td>
                          <td class="border-0 py-1 pe-0 ps-3 ps-sm-4">
                            <div class="fs-sm text-body-secondary mb-2">
                              Quantity
                            </div>
                            <div class="fs-sm fw-medium text-dark">
                              {{ data.quantity }}
                            </div>
                          </td>
                          <td class="border-0 py-1 pe-0 ps-3 ps-sm-4">
                            <div class="fs-sm text-body-secondary mb-2">
                              Price
                            </div>
                            <div class="fs-sm fw-medium text-dark">
                              {{ selectedCurrency }}{{ data.price }}
                            </div>
                          </td>
                          <td class="border-0 text-end py-1 pe-0 ps-3 ps-sm-4">
                            <div class="fs-sm text-body-secondary mb-2">
                              Total
                            </div>
                            <div class="fs-sm fw-medium text-dark">
                              {{ selectedCurrency
                              }}{{ data.quantity * data.price }}
                            </div>
                          </td>
                        </tr>
                      }
                    </tbody>
                  </table>
                </div>
                <div class="bg-secondary rounded-1 p-4 my-2">
                  <div class="row">
                    <div class="col-sm-5 col-md-3 col-lg-4 mb-3 mb-md-0">
                      <div class="fs-sm fw-medium text-dark mb-1">Payment:</div>
                      <div class="fs-sm">Upon the delivery</div>
                      <a
                        class="btn btn-link py-1 px-0 mt-2"
                        href="javascript:void(0);"
                      >
                        <i class="ai-time me-2 ms-n1"></i>
                        Order history
                      </a>
                    </div>
                    <div class="col-sm-7 col-md-5 mb-4 mb-md-0">
                      <div class="fs-sm fw-medium text-dark mb-1">
                        Delivery address:
                      </div>
                      <div class="fs-sm">
                        401 Magnetic Drive Unit 2,<br />Toronto, Ontario, M3J
                        3H9, Canada
                      </div>
                    </div>
                    <div class="col-md-4 col-lg-3 text-md-end">
                      <button
                        class="btn btn-sm btn-outline-primary w-100 w-md-auto"
                        type="button"
                      >
                        <i class="ai-star me-2 ms-n1"></i>
                        Leave a review
                      </button>
                    </div>
                  </div>
                </div>
              </ng-template>
            </div>
          </div>
        </div>
      }
    </div>
  </div>
</section>
